<template>
  <div class="admission-record-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient" />

    <!-- 右侧入院记录内容区域 -->
    <div class="record-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载入院记录...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else>
        <!-- 页面标题、时间信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">入院记录</h2>
          <div class="header-actions">
            <div class="time-info-section">
              <div class="time-item">
                <span class="time-label">创建时间：</span>
                <span class="time-value">{{ recordData.recordDatetime }}</span>
              </div>
              <div class="time-item">
                <span class="time-label">更新时间：</span>
                <span class="time-value">{{ recordData.recordUpdateDatetime }}</span>
              </div>
            </div>
            <div class="form-actions">
              <el-button
                  class="delete-btn"
                  @click="handleDelete"
                  :loading="deleteLoading"
                  :disabled="!recordData.recordSn"
              >
                删除
              </el-button>
              <el-button
                  type="primary"
                  class="save-btn"
                  @click="handleSave"
                  :loading="saveLoading"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>

      <!-- 入院记录表单 -->
      <div class="record-form">
        <!-- 主诉 -->
        <div class="form-item">
          <label class="form-label">主诉：</label>
          <el-input
            v-model="formData.chiefComplaint"
            type="textarea"
            placeholder="请输入内容"
            :rows="2"
            class="form-textarea small"
          />
        </div>

        <!-- 现病史 -->
        <div class="form-item">
          <label class="form-label">现病史：</label>
          <el-input
            ref="currentMedhistoryRef"
            v-model="formData.currentMedhistory"
            type="textarea"
            placeholder="请输入内容"
            class="form-textarea autosize-current-medhistory"
            @input="handleCurrentMedhistoryInput"
          />
        </div>

        <!-- 既往史 -->
        <div class="form-item">
          <label class="form-label">既往史：</label>
          <el-input
            ref="pastMedhistoryRef"
            v-model="formData.pastMedhistory"
            type="textarea"
            placeholder="请输入内容"
            class="form-textarea autosize-past-medhistory"
            @input="handlePastMedhistoryInput"
          />
        </div>

        <!-- 个人史和月经史 - 同行显示 -->
        <div class="form-item-row">
          <div class="form-item-half">
            <label class="form-label">个人史：</label>
            <el-input
              v-model="formData.personalMedhistory"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>
          <div class="form-item-half">
            <label class="form-label">月经史：</label>
            <el-input
              v-model="formData.menstrualHistory"
              type="textarea"
              placeholder="请输入内容"
              :rows="2"
              class="form-textarea small"
            />
          </div>
        </div>

        <!-- 婚育史 -->
        <div class="form-item">
          <label class="form-label">婚育史：</label>
          <el-input
            ref="marriageBirthHistoryRef"
            v-model="formData.marriageBirthHistory"
            type="textarea"
            placeholder="请输入内容"
            class="form-textarea autosize-marriage-birth-history"
            @input="handleMarriageBirthHistoryInput"
          />
        </div>

        <!-- 家族史 -->
        <div class="form-item">
          <label class="form-label">家族史：</label>
          <el-input
            v-model="formData.familyHistory"
            type="textarea"
            placeholder="请输入内容"
            :rows="2"
            class="form-textarea small"
          />
        </div>

        <!-- 体格检查 -->
        <div class="form-item">
          <label class="form-label">体格检查：</label>
          <el-input
              ref="physicalExamRef"
              v-model="formData.physicalExam"
              type="textarea"
              placeholder="请输入内容"
              class="form-textarea autosize-physical-exam"
              @input="handlePhysicalExamInput"
          />
        </div>

        <!-- 辅助检查 -->
        <div class="form-item">
          <label class="form-label">辅助检查：</label>
          <el-input
            ref="auxiliaryExamRef"
            v-model="formData.auxiliaryExam"
            type="textarea"
            placeholder="请输入内容"
            class="form-textarea autosize-auxiliary-exam"
            @input="handleAuxiliaryExamInput"
          />
        </div>


        <!-- 入院初步诊断 -->
        <div class="form-item">
          <label class="form-label">入院初步诊断：</label>
          <el-input
            v-model="formData.primaryDiagnosis"
            type="textarea"
            placeholder="请输点击选择诊断信息"
            :rows="2"
            class="form-textarea small diagnosis-clickable"
            @click="handleDiagnosisInputClick"
          />
        </div>
      </div>


      </div>
    </div>

    <!-- 诊断信息选择对话框 -->
    <DiagnosisSelector
      v-model="showDiagnosisSelector"
      :patient="patient"
      :diag-type="'入院诊断'"
      @confirm="handleDiagnosisConfirm"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { apiServices } from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'
import DiagnosisSelector from './DiagnosisSelector.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['patient-data-updated'])

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)
const showDiagnosisSelector = ref(false)

// 文本框引用
const currentMedhistoryRef = ref(null)
const pastMedhistoryRef = ref(null)
const marriageBirthHistoryRef = ref(null)
const physicalExamRef = ref(null)
const auxiliaryExamRef = ref(null)

// 入院记录数据
const recordData = reactive({
  recordDatetime: '',
  recordUpdateDatetime: '',
  recordSn: '',
  visitSn: ''
})

// 表单数据
const formData = reactive({
  chiefComplaint: '',
  currentMedhistory: '',
  pastMedhistory: '',
  personalMedhistory: '',
  menstrualHistory: '',
  marriageBirthHistory: '',
  familyHistory: '',
  auxiliaryExam: '',
  physicalExam: '',
  primaryDiagnosis: ''
})





// 加载入院记录数据
const loadAdmissionRecord = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载入院记录')
    return
  }

  try {
    loading.value = true
    console.log('正在加载入院记录，visitSn:', props.patient.visitSn)

    const data = await apiServices.admissionRecord.getDetail(props.patient.visitSn)

    // 更新记录数据
    Object.assign(recordData, {
      recordDatetime: data.recordDatetime || '',
      recordUpdateDatetime: data.recordUpdateDatetime || '',
      recordSn: data.recordSn || '',
      visitSn: data.visitSn || props.patient.visitSn
    })

    // 更新表单数据
    Object.assign(formData, {
      chiefComplaint: data.chiefComplaint || '',
      currentMedhistory: data.currentMedhistory || '',
      pastMedhistory: data.pastMedhistory || '',
      personalMedhistory: data.personalMedhistory || '',
      menstrualHistory: data.menstrualHistory || '',
      marriageBirthHistory: data.marriageBirthHistory || '',
      familyHistory: data.familyHistory || '',
      auxiliaryExam: data.auxiliaryExam || '',
      physicalExam: data.physicalExam || '',
      primaryDiagnosis: data.primaryDiagnosis || ''
    })

    console.log('入院记录加载成功:', data)

    // 强制初始化文本框高度
    setTimeout(() => {
      forceInitTextareaHeight()
    }, 100)
  } catch (error) {
    console.error('加载入院记录失败:', error)
    ElMessage.error('加载入院记录失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 处理保存操作
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      recordSn: recordData.recordSn,
      ...formData
    }

    console.log('正在保存入院记录:', saveData)

    const result = await apiServices.admissionRecord.save(saveData)

    // 更新记录信息
    if (result.recordSn) {
      recordData.recordSn = result.recordSn
    }
    if (result.recordDatetime) {
      recordData.recordDatetime = result.recordDatetime
    }
    if (result.recordUpdateDatetime) {
      recordData.recordUpdateDatetime = result.recordUpdateDatetime
    }

    ElMessage.success('保存成功')
    console.log('入院记录保存成功:', result)

    // 通知父组件患者数据已更新，需要重新加载
    emit('patient-data-updated')
  } catch (error) {
    console.error('保存入院记录失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

// 处理删除操作
const handleDelete = async () => {
  if (!recordData.recordSn) {
    ElMessage.warning('当前没有可删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(
      '确定要删除这条入院记录吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    deleteLoading.value = true

    console.log('正在删除入院记录，visitSn:', props.patient.visitSn)

    await apiServices.admissionRecord.delete(props.patient.visitSn)

    // 清空表单数据
    Object.assign(formData, {
      chiefComplaint: '',
      currentMedhistory: '',
      pastMedhistory: '',
      personalMedhistory: '',
      menstrualHistory: '',
      marriageBirthHistory: '',
      familyHistory: '',
      auxiliaryExam: '',
      physicalExam: '',
      primaryDiagnosis: ''
    })

    // 清空记录数据
    Object.assign(recordData, {
      recordDatetime: '',
      recordUpdateDatetime: '',
      recordSn: '',
      visitSn: props.patient.visitSn
    })

    ElMessage.success('删除成功')
    console.log('入院记录删除成功')

    // 通知父组件患者数据已更新，需要重新加载
    emit('patient-data-updated')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除入院记录失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// ========== 文本框自适应高度和滚动优化 ==========

/**
 * 处理现病史输入变化
 */
const handleCurrentMedhistoryInput = () => {
  nextTick(() => {
    adjustCurrentMedhistoryHeight()
  })
}

/**
 * 处理既往史输入变化
 */
const handlePastMedhistoryInput = () => {
  nextTick(() => {
    adjustPastMedhistoryHeight()
  })
}

/**
 * 处理婚育史输入变化
 */
const handleMarriageBirthHistoryInput = () => {
  nextTick(() => {
    adjustMarriageBirthHistoryHeight()
  })
}

/**
 * 处理体格检查输入变化
 */
const handlePhysicalExamInput = () => {
  nextTick(() => {
    adjustPhysicalExamHeight()
  })
}

/**
 * 处理辅助检查输入变化
 */
const handleAuxiliaryExamInput = () => {
  nextTick(() => {
    adjustAuxiliaryExamHeight()
  })
}

/**
 * 强制初始化文本框高度（用于数据加载后）
 */
const forceInitTextareaHeight = () => {
  // 多次尝试，确保成功
  let attempts = 0
  const maxAttempts = 5

  const tryAdjust = () => {
    attempts++

    if (currentMedhistoryRef.value && pastMedhistoryRef.value &&
        marriageBirthHistoryRef.value && auxiliaryExamRef.value &&
        physicalExamRef.value) {
      adjustCurrentMedhistoryHeight()
      adjustPastMedhistoryHeight()
      adjustMarriageBirthHistoryHeight()
      adjustAuxiliaryExamHeight()
      adjustPhysicalExamHeight()
    } else if (attempts < maxAttempts) {
      setTimeout(tryAdjust, 100)
    }
  }

  tryAdjust()
}

/**
 * 动态调整现病史文本框高度
 */
const adjustCurrentMedhistoryHeight = () => {
  if (!currentMedhistoryRef.value) return

  try {
    const textareaElement = currentMedhistoryRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) return

    const currentValue = formData.currentMedhistory || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    textareaElement.style.height = 'auto'
    textareaElement.offsetHeight

    const scrollHeight = textareaElement.scrollHeight
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 3行的最小高度
    const minHeight = lineHeight * 3 + paddingTop + paddingBottom + borderTop + borderBottom
    const finalHeight = Math.max(scrollHeight + 2, minHeight)

    textareaElement.style.height = finalHeight + 'px'
  } catch (error) {
  }
}

/**
 * 动态调整既往史文本框高度
 */
const adjustPastMedhistoryHeight = () => {
  if (!pastMedhistoryRef.value) return

  try {
    const textareaElement = pastMedhistoryRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) return

    const currentValue = formData.pastMedhistory || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    textareaElement.style.height = 'auto'
    textareaElement.offsetHeight

    const scrollHeight = textareaElement.scrollHeight
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 2行的最小高度
    const minHeight = lineHeight * 2 + paddingTop + paddingBottom + borderTop + borderBottom
    const finalHeight = Math.max(scrollHeight + 2, minHeight)

    textareaElement.style.height = finalHeight + 'px'
  } catch (error) {
  }
}

/**
 * 动态调整婚育史文本框高度
 */
const adjustMarriageBirthHistoryHeight = () => {
  if (!marriageBirthHistoryRef.value) return

  try {
    const textareaElement = marriageBirthHistoryRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) return

    const currentValue = formData.marriageBirthHistory || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    textareaElement.style.height = 'auto'
    textareaElement.offsetHeight

    const scrollHeight = textareaElement.scrollHeight
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 2行的最小高度
    const minHeight = lineHeight * 2 + paddingTop + paddingBottom + borderTop + borderBottom
    const finalHeight = Math.max(scrollHeight + 2, minHeight)

    textareaElement.style.height = finalHeight + 'px'
  } catch (error) {
  }
}

/**
 * 动态调整辅助检查文本框高度
 */
const adjustAuxiliaryExamHeight = () => {
  if (!auxiliaryExamRef.value) return

  try {
    const textareaElement = auxiliaryExamRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) return

    const currentValue = formData.auxiliaryExam || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    textareaElement.style.height = 'auto'
    textareaElement.offsetHeight

    const scrollHeight = textareaElement.scrollHeight
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 2行的最小高度
    const minHeight = lineHeight * 2 + paddingTop + paddingBottom + borderTop + borderBottom
    const finalHeight = Math.max(scrollHeight + 2, minHeight)

    textareaElement.style.height = finalHeight + 'px'
  } catch (error) {
  }
}

/**
 * 动态调整体格检查文本框高度
 */
const adjustPhysicalExamHeight = () => {
  if (!physicalExamRef.value) return

  try {
    const textareaElement = physicalExamRef.value.$el.querySelector('.el-textarea__inner')
    if (!textareaElement) return

    const currentValue = formData.physicalExam || ''
    if (textareaElement.value !== currentValue) {
      textareaElement.value = currentValue
    }

    textareaElement.style.height = 'auto'
    textareaElement.offsetHeight

    const scrollHeight = textareaElement.scrollHeight
    const computedStyle = window.getComputedStyle(textareaElement)
    const fontSize = parseFloat(computedStyle.fontSize) || 14
    const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.6
    const paddingTop = parseFloat(computedStyle.paddingTop) || 12
    const paddingBottom = parseFloat(computedStyle.paddingBottom) || 12
    const borderTop = parseFloat(computedStyle.borderTopWidth) || 1
    const borderBottom = parseFloat(computedStyle.borderBottomWidth) || 1

    // 2行的最小高度
    const minHeight = lineHeight * 2 + paddingTop + paddingBottom + borderTop + borderBottom
    const finalHeight = Math.max(scrollHeight + 2, minHeight)

    textareaElement.style.height = finalHeight + 'px'
  } catch (error) {
  }
}

// ========== 诊断信息选择相关方法 ==========

/**
 * 处理诊断输入框点击
 */
const handleDiagnosisInputClick = () => {
  showDiagnosisSelector.value = true
}



/**
 * 处理诊断信息确认选择
 */
const handleDiagnosisConfirm = (diagnosisText) => {
  console.log('返回的诊断信息:', diagnosisText)

  // 将拼接的诊断信息字符串填入输入框
  if (diagnosisText) {
    formData.primaryDiagnosis = diagnosisText
    ElMessage.success('诊断信息选择成功')
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadAdmissionRecord()
  }
}, { immediate: false })

// 监听现病史内容变化，自动调整高度
watch(() => formData.currentMedhistory, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustCurrentMedhistoryHeight()
        }, 10)
      })
    })
  }
}, { flush: 'post' })

// 监听既往史内容变化，自动调整高度
watch(() => formData.pastMedhistory, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustPastMedhistoryHeight()
        }, 10)
      })
    })
  }
}, { flush: 'post' })

// 监听婚育史内容变化，自动调整高度
watch(() => formData.marriageBirthHistory, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustMarriageBirthHistoryHeight()
        }, 10)
      })
    })
  }
}, { flush: 'post' })

// 监听辅助检查内容变化，自动调整高度
watch(() => formData.auxiliaryExam, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustAuxiliaryExamHeight()
        }, 10)
      })
    })
  }
}, { flush: 'post' })

// 监听体格检查内容变化，自动调整高度
watch(() => formData.physicalExam, (newValue, oldValue) => {
  if (newValue !== oldValue) {
    nextTick(() => {
      nextTick(() => {
        setTimeout(() => {
          adjustPhysicalExamHeight()
        }, 10)
      })
    })
  }
}, { flush: 'post' })

// 组件挂载时加载数据
onMounted(() => {
  if (props.patient?.visitSn) {
    loadAdmissionRecord()
  } else {
    // 即使没有数据也要初始化文本框高度
    nextTick(() => {
      nextTick(() => {
        adjustCurrentMedhistoryHeight()
        adjustPastMedhistoryHeight()
        adjustMarriageBirthHistoryHeight()
        adjustAuxiliaryExamHeight()
        adjustPhysicalExamHeight()
      })
    })
  }
})
</script>

<style scoped>
/* 整体容器 */
.admission-record-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px); /* 使用视口高度减去导航栏高度，确保一屏显示 */
  gap: 20px;
}



/* 右侧入院记录内容区域 */
.record-content-area {
  flex: 1;
  height: 100%; /* 使用100%高度，与容器保持一致 */
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px; /* 减少内边距 */
  display: flex;
  flex-direction: column;
  overflow-y: auto; /* 添加垂直滚动，以防内容过多 */
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 页面标题、时间信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0; /* 防止压缩 */
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  width: auto; /* 自动宽度 */
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0; /* 移除默认margin */
}

/* 时间信息头部 */
.time-info-section {
  display: flex;
  gap: 40px;
}

.time-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.time-label {
  width: 80px;
  height: 20px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
}

.time-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

/* 表单区域 */
.record-form {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px; /* 减少表单项之间的间距 */
  min-height: 0; /* 允许内容压缩 */
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

/* 两列表单行样式 */
.form-item-row {
  display: flex;
  gap: 16px; /* 减少列间距 */
  width: 100%;
}

.form-item-half {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
  /* 设置最小宽度以确保标签和输入框有足够空间 */
  min-width: calc(130px + 12px + 200px); /* 标签宽度 + 间距 + 最小输入框宽度 */
}

.form-label {
  width: 130px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
  text-align: right;
  margin-top: 8px;
  flex-shrink: 0;
}

/* 确保两列布局中的标签与单列布局保持一致的对齐 */
.form-item-half .form-label {
  width: 130px; /* 与单列布局保持相同宽度，确保冒号垂直对齐 */
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .form-item-row {
    flex-direction: column;
    gap: 20px;
  }

  .form-item-half {
    min-width: auto; /* 移除最小宽度限制 */
  }

  .form-item-half .form-label {
    width: 130px; /* 保持标签宽度一致 */
  }
  .time-info-section{
    display: none;
  }
}

/* 文本框样式 */
.form-textarea {
  width: 100%;
}

/* 现病史自适应高度文本框专用样式 - 使用高优先级选择器 */
.admission-record-container .record-content-area .form-textarea.autosize-current-medhistory :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 既往史自适应高度文本框专用样式 - 使用高优先级选择器 */
.admission-record-container .record-content-area .form-textarea.autosize-past-medhistory :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 婚育史自适应高度文本框专用样式 - 使用高优先级选择器 */
.admission-record-container .record-content-area .form-textarea.autosize-marriage-birth-history :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 辅助检查自适应高度文本框专用样式 - 使用高优先级选择器 */
.admission-record-container .record-content-area .form-textarea.autosize-auxiliary-exam :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 体格检查自适应高度文本框专用样式 - 使用高优先级选择器 */
.admission-record-container .record-content-area .form-textarea.autosize-physical-exam :deep(.el-textarea__inner) {
  background: #FFFFFF !important;
  border-radius: 4px !important;
  border: 1px solid #EBECF0 !important;
  font-family: 'PingFang SC', sans-serif !important;
  font-size: 14px !important;
  color: #172B4D !important;
  resize: none !important;
  line-height: 1.6 !important;
  padding: 5px 12px !important;
  box-sizing: border-box !important;
  /* 隐藏滚动条 */
  overflow-y: hidden !important;
  /* 不设置min-height和height，完全由JavaScript动态控制 */
}

/* 非自适应的小尺寸文本框样式（固定行数的文本框） */
.form-textarea.small:not(.autosize) :deep(.el-textarea__inner) {
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.5; /* 优化行高，确保文本有足够空间 */
  padding: 5px 12px; /* 较小的内边距适合固定高度 */
  box-sizing: border-box; /* 确保盒模型计算正确 */
}

/* 移除冲突的固定高度样式，现病史使用 .form-textarea.medium.autosize 自适应样式 */

/* 文本框聚焦状态 */
.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;
}

/* 现病史文本框聚焦状态 */
.admission-record-container .record-content-area .form-textarea.autosize-current-medhistory :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 既往史文本框聚焦状态 */
.admission-record-container .record-content-area .form-textarea.autosize-past-medhistory :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 婚育史文本框聚焦状态 */
.admission-record-container .record-content-area .form-textarea.autosize-marriage-birth-history :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 辅助检查文本框聚焦状态 */
.admission-record-container .record-content-area .form-textarea.autosize-auxiliary-exam :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 体格检查文本框聚焦状态 */
.admission-record-container .record-content-area .form-textarea.autosize-physical-exam :deep(.el-textarea__inner:focus) {
  border-color: #1678FF !important;
}

/* 文本框占位符样式 */
.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0; /* 防止压缩 */
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 入院初步诊断输入框点击样式 */
.diagnosis-clickable :deep(.el-textarea__inner) {
  cursor: pointer;
}

.diagnosis-clickable :deep(.el-textarea__inner:hover) {
  border-color: #1678FF;
}
</style>
