<template>
  <div class="exam-report-container">
    <!-- 左侧患者信息详情面板 -->
    <PatientInfoPanel :patient="patient"/>

    <!-- 右侧检查报告内容区域 -->
    <div class="report-content-area">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="loading" element-loading-text="正在加载检查报告...">
        <div style="height: 200px;"></div>
      </div>

      <!-- 内容区域 -->
      <div v-else class="content-wrapper">
        <!-- 页面标题、基本信息和按钮行 -->
        <div class="page-header">
          <h2 class="title-text">检查报告</h2>
          <div class="header-actions">
            <div v-if="selectedReportIndex !== -1" class="info-section">
              <div class="info-item">
                <span class="info-label">检查项目：</span>
                <span class="info-value">{{ formData.examItemName }}</span>
              </div>

              <div class="info-item time-info-section">
                <span class="info-label">检查时间：</span>
                <span class="info-value">{{ formData.examDatetime }}</span>
              </div>
              <div class="info-item time-info-section">
                <span class="info-label">申请时间：</span>
                <span class="info-value">{{ formData.applyDatetime }}</span>
              </div>


            </div>
            <div class="form-actions">
              <!--              <el-button-->
              <!--                class="delete-btn"-->
              <!--                @click="handleDelete"-->
              <!--                :loading="deleteLoading"-->
              <!--                :disabled="selectedReportIndex === -1"-->
              <!--              >-->
              <!--                删除-->
              <!--              </el-button>-->
              <el-button
                  type="primary"
                  class="save-btn"
                  @click="handleSave"
                  :loading="saveLoading"
                  :disabled="selectedReportIndex === -1"
              >
                保存
              </el-button>
            </div>
          </div>
        </div>

        <!-- 检查报告表格区域 -->
        <div class="exam-report-tables-container">
          <!-- 左侧报告列表表格 -->
          <div class="report-table-container">
            <div class="table-wrapper">
              <table class="report-table">
                <!-- 表头 -->
                <thead>
                <tr class="table-header">
                  <th class="header-cell device-type-col">设备类型</th>
                  <th class="header-cell exam-site-col">检查部位</th>
                  <th class="header-cell report-time-col">报告时间</th>
                </tr>
                </thead>
                <!-- 表体 -->
                <tbody>
                <tr
                    v-for="(report, index) in reportList"
                    :key="report.reportNo || index"
                    class="table-row"
                    :class="{ 'selected': selectedReportIndex === index }"
                    @click="handleReportSelect(report, index)"
                >
                  <td class="table-cell device-type-col">{{ report.examItemType }}</td>
                  <td class="table-cell exam-site-col">{{ report.examSites }}</td>
                  <td class="table-cell report-time-col">{{ report.recordDatetime }}</td>
                </tr>
                <!-- 空数据提示 -->
                <tr v-if="reportList.length === 0" class="empty-row">
                  <td colspan="3" class="empty-cell">暂无检查报告数据</td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 右侧表单区域 -->
          <div class="form-container">
            <div v-if="selectedReportIndex !== -1" class="report-form">
              <!-- 影像所见 -->
              <div class="form-section">
                <div class="section-title">影像所见：</div>
                <el-input
                    v-model="formData.examDiagDescription"
                    type="textarea"
                    placeholder="请输入影像所见"
                    :rows="8"
                    class="form-textarea"
                />
              </div>

              <!-- 影像结论 -->
              <div class="form-section">
                <div class="section-title">影像结论：</div>
                <el-input
                    v-model="formData.examDiagConclusion"
                    type="textarea"
                    placeholder="请输入影像结论"
                    :rows="8"
                    class="form-textarea"
                />
              </div>


            </div>

            <!-- 未选择报告时的提示 -->
            <div v-else class="no-selection">
              <div class="no-selection-text">请选择左侧的检查报告查看详情</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, onMounted, watch} from 'vue'
import {ElMessage, ElMessageBox} from 'element-plus'
import {apiServices} from '@/api'
import PatientInfoPanel from './PatientInfoPanel.vue'

// 定义组件props
const props = defineProps({
  patient: {
    type: Object,
    required: true,
    default: () => ({})
  }
})

// 定义组件事件
const emit = defineEmits(['count-updated'])

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const deleteLoading = ref(false)

// 报告列表和当前选中报告
const reportList = ref([])
const selectedReportIndex = ref(-1)
const selectedReportNo = ref('')
const currentReport = reactive({
  visitSn: '',
  reportNo: '',
  examItemType: '',
  examSites: '',
  recordDatetime: ''
})

// 表单数据
const formData = reactive({
  examDatetime: '',
  applyDatetime: '',
  examItemName: '',
  examDiagDescription: '',
  examDiagConclusion: ''
})

// ========== 数据加载方法 ==========

/**
 * 加载检查报告列表
 */
const loadReportList = async () => {
  if (!props.patient?.visitSn) {
    console.warn('缺少visitSn，无法加载检查报告列表')
    return
  }

  try {
    loading.value = true
    console.log('正在加载检查报告列表，visitSn:', props.patient.visitSn)

    const reports = await apiServices.examReport.getList(props.patient.visitSn)

    // 按报告时间倒序排列（最新的在前面）
    reportList.value = reports.sort((a, b) => {
      return new Date(b.recordDatetime) - new Date(a.recordDatetime)
    })

    console.log('检查报告列表加载成功:', reportList.value)

    // 自动选中第一条记录
    if (reportList.value.length > 0 && selectedReportIndex.value === -1) {
      await handleReportSelect(reportList.value[0], 0)
    } else if (reportList.value.length === 0) {
      // 没有记录时清空当前报告和表单
      clearCurrentReport()
    }
  } catch (error) {
    console.error('加载检查报告列表失败:', error)
    ElMessage.error('加载检查报告列表失败: ' + error.message)
    reportList.value = []
    clearCurrentReport()
  } finally {
    loading.value = false
  }
}

/**
 * 清空当前报告
 */
const clearCurrentReport = () => {
  Object.assign(currentReport, {
    visitSn: props.patient?.visitSn || '',
    reportNo: '',
    examItemType: '',
    examSites: '',
    recordDatetime: ''
  })
  Object.assign(formData, {
    examDatetime: '',
    applyDatetime: '',
    examItemName: '',
    examDiagDescription: '',
    examDiagConclusion: ''
  })
  selectedReportIndex.value = -1
  selectedReportNo.value = ''
}

// ========== 事件处理方法 ==========

/**
 * 处理报告选择
 */
const handleReportSelect = async (report, index) => {
  selectedReportIndex.value = index
  selectedReportNo.value = report.reportNo

  // 更新当前报告数据
  Object.assign(currentReport, {
    visitSn: report.visitSn,
    reportNo: report.reportNo,
    examItemType: report.examItemType,
    examSites: report.examSites,
    recordDatetime: report.recordDatetime
  })

  // 更新表单数据
  Object.assign(formData, {
    examDatetime: report.examDatetime,
    applyDatetime: report.applyDatetime,
    examItemName: report.examItemName,
    examDiagDescription: report.examDiagDescription,
    examDiagConclusion: report.examDiagConclusion
  })
}

/**
 * 保存检查报告
 */
const handleSave = async () => {
  if (!props.patient?.visitSn) {
    ElMessage.error('缺少患者信息，无法保存')
    return
  }

  if (selectedReportIndex.value === -1 || !selectedReportNo.value) {
    ElMessage.error('请选择要保存的报告')
    return
  }

  try {
    saveLoading.value = true

    // 构建保存数据
    const saveData = {
      visitSn: props.patient.visitSn,
      reportNo: currentReport.reportNo,
      examItemType: currentReport.examItemType,
      examSites: currentReport.examSites,
      recordDatetime: currentReport.recordDatetime,
      examDatetime: formData.examDatetime,
      applyDatetime: formData.applyDatetime,
      examItemName: formData.examItemName,
      examDiagDescription: formData.examDiagDescription.trim(),
      examDiagConclusion: formData.examDiagConclusion
    }

    console.log('正在保存检查报告:', saveData)

    await apiServices.examReport.update(saveData)

    ElMessage.success('保存成功')
    console.log('检查报告保存成功')

    // 重新加载列表
    await loadReportList()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    console.error('保存检查报告失败:', error)
    ElMessage.error('保存失败: ' + error.message)
  } finally {
    saveLoading.value = false
  }
}

/**
 * 删除检查报告
 */
const handleDelete = async () => {
  if (selectedReportIndex.value === -1 || !selectedReportNo.value) {
    ElMessage.error('没有可删除的报告')
    return
  }

  try {
    await ElMessageBox.confirm(
        '确定要删除这条检查报告吗？删除后无法恢复。',
        '确认删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
    )

    deleteLoading.value = true

    console.log('正在删除检查报告，reportNo:', selectedReportNo.value)

    await apiServices.examReport.delete(selectedReportNo.value)

    ElMessage.success('删除成功')
    console.log('检查报告删除成功')

    // 清空当前选中状态
    selectedReportIndex.value = -1
    selectedReportNo.value = ''

    // 重新加载列表
    await loadReportList()

    // 触发数量更新事件
    emit('count-updated')
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除检查报告失败:', error)
      ElMessage.error('删除失败: ' + error.message)
    }
  } finally {
    deleteLoading.value = false
  }
}

// 监听患者变化，重新加载数据
watch(() => props.patient?.visitSn, (newVisitSn) => {
  if (newVisitSn) {
    loadReportList()
  }
}, {immediate: false})

// 组件挂载时加载数据
onMounted(async () => {
  if (props.patient?.visitSn) {
    await loadReportList()
  }
})
</script>

<style scoped>
/* 整体容器 */
.exam-report-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 180px);
  gap: 20px;
}

/* 右侧报告内容区域 */
.report-content-area {
  flex: 1;
  height: 100%;
  background: #FFFFFF;
  border-radius: 0 12px 12px 0;
  padding: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

/* 内容包装器 */
.content-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 页面标题、基本信息和按钮行 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 40px;
}

.title-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 18px;
  color: #172B4D;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin: 0;
}

/* 检查报告表格区域 */
.exam-report-tables-container {
  display: flex;
  flex: 1;
  overflow: hidden;
  gap: 20px;
  min-height: 0; /* 确保flex子项能正确收缩 */
  min-width: 0; /* 确保能够收缩 */
}

/* 左侧报告表格容器 */
.report-table-container {
  flex-shrink: 0;
  width: 50%;
}

.table-wrapper {
  height: 100%;
  overflow: auto;
  border: 1px solid #EBECF0;
  border-radius: 4px;
  background: #FFFFFF;
}

/* 表格样式 */
.report-table {
  width: 100%;
  border-collapse: collapse;
  font-family: 'PingFang SC', sans-serif;
}

.table-header {
  background: #F8F9FA;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-cell {
  padding: 12px 16px;
  text-align: left;
  font-weight: 500;
  font-size: 14px;
  color: #172B4D;
  border-bottom: 1px solid #EBECF0;
  border-right: 1px solid #EBECF0;
}

.header-cell:last-child {
  border-right: none;
}

/* 列宽设置 */
.device-type-col {
  width: 100px;
}

.exam-site-col {
  width: 180px;
}

.report-time-col {
  width: 150px;
  white-space: nowrap;
}

.table-row {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #F4F5F7;
}

.table-row.selected {
  background: #E6F1FF;
}

.table-cell {
  padding: 12px 16px;
  font-size: 14px;
  color: #172B4D;
  border-bottom: 1px solid #EBECF0;
  border-right: 1px solid #EBECF0;
  word-break: break-all;
}

.table-cell:last-child {
  border-right: none;
}

.empty-row {
  background: #FAFCFF;
}

.empty-cell {
  padding: 40px 16px;
  text-align: center;
  font-size: 14px;
  color: #6B778C;
  border-bottom: 1px solid #EBECF0;
}

/* 右侧表单容器 */
.form-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden; /* 隐藏水平滚动条 */
  min-height: 0; /* 确保flex子项能正确收缩 */
}

/* 自定义滚动条样式 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.report-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0 4px 20px 0; /* 为滚动条留出空间，底部留出间距 */
  min-width: 0; /* 确保能够收缩 */
  width: 100%; /* 确保占满容器宽度 */
}

/* 基本信息区域 */
.info-section {
  display: flex;
  gap: 40px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.info-label {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #6B778C;
}

.info-value {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #172B4D;
}

/* 表单区域 */
.form-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 200px; /* 确保足够的高度 */
  min-width: 0; /* 确保能够收缩 */
  width: 100%; /* 确保占满容器宽度 */
}

.section-title {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 500;
  font-size: 16px;
  color: #172B4D;
  line-height: 20px;
}

.form-textarea {
  flex: 1;
  width: 100%; /* 确保占满容器宽度 */
  min-width: 0; /* 确保能够收缩 */
}

.form-textarea :deep(.el-textarea__inner) {
  height: 100% !important;
  background: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #EBECF0;
  font-family: 'PingFang SC', sans-serif;
  font-size: 14px;
  color: #172B4D;
  resize: none;
  line-height: 1.5;
  padding: 12px;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  border-color: #1678FF;

}

.form-textarea :deep(.el-textarea__inner::placeholder) {
  color: #C1C7D0;
  font-family: 'PingFang SC', sans-serif;
}

/* 按钮区域 */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  flex-shrink: 0;
}

.delete-btn {
  width: 80px;
  height: 36px;
  background: #FFFFFF;
  border: 1px solid #FF5630;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FF5630;
  line-height: 20px;
}

.delete-btn:hover {
  background: #FFF2F0;
  border-color: #FF5630;
  color: #FF5630;
}

.save-btn {
  width: 80px;
  height: 36px;
  background: #1678FF;
  border: 1px solid #1678FF;
  border-radius: 4px;
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;
  line-height: 20px;
}

.save-btn:hover {
  background: #0052CC;
  border-color: #0052CC;
}

/* 未选择报告时的提示 */
.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-selection-text {
  font-family: 'PingFang SC', sans-serif;
  font-weight: 400;
  font-size: 16px;
  color: #6B778C;
  line-height: 20px;
}

/* 响应式布局 - 当空间不足时改为单列 */
@media (max-width: 1200px) {
  .time-info-section {
    display: none;
  }
}
</style>
